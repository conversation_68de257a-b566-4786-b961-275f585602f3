<<<<<<< HEAD
import React, { useState, useCallback } from 'react';
=======
import React, { useState } from 'react';
>>>>>>> 90b8cac (feat: add React frontend application with modern UI #23)
import { motion } from 'framer-motion';

interface AtmosphereSliderProps {
  label: string;
  defaultValue: number;
<<<<<<< HEAD
  onChange?: (value: number) => void;
}

const AtmosphereSlider: React.FC<AtmosphereSliderProps> = ({
  label,
  defaultValue,
  onChange
}) => {
  const [value, setValue] = useState(defaultValue);
  const [isHovering, setIsHovering] = useState(false);

  const handleChange = useCallback((newValue: number) => {
    setValue(newValue);
    onChange?.(newValue);
  }, [onChange]);

  return (
    <div className="atmosphere-slider-container space-y-3">
=======
}

const AtmosphereSlider: React.FC<AtmosphereSliderProps> = ({ 
  label, 
  defaultValue 
}) => {
  const [value, setValue] = useState(defaultValue);

  return (
    <div className="space-y-3">
>>>>>>> 90b8cac (feat: add React frontend application with modern UI #23)
      <div className="flex justify-between items-center">
        <span className="text-white/80 font-medium">{label}</span>
        <span className="text-white/60 text-sm">{value}%</span>
      </div>
<<<<<<< HEAD

      <div
        className="relative"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        {/* Track Background */}
=======
      
      <div className="relative">
>>>>>>> 90b8cac (feat: add React frontend application with modern UI #23)
        <div className="h-3 bg-white/10 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-accent-from to-accent-to rounded-full"
            style={{ width: `${value}%` }}
            initial={{ width: 0 }}
            animate={{ width: `${value}%` }}
            transition={{ duration: 0.3 }}
          />
<<<<<<< HEAD

          <motion.div
            className="absolute top-0 h-full bg-gradient-to-r from-accent-from to-accent-to rounded-full blur-sm"
            style={{ width: `${value}%` }}
            initial={{ width: 0, opacity: 0.3 }}
            animate={{
              width: `${value}%`,
              opacity: isHovering ? 0.6 : 0.3
            }}
            transition={{ duration: 0.3 }}
          />
        </div>

        {/* Range Input */}
=======
          
          <motion.div
            className="absolute top-0 h-full bg-gradient-to-r from-accent-from to-accent-to rounded-full blur-sm opacity-50"
            style={{ width: `${value}%` }}
            initial={{ width: 0 }}
            animate={{ width: `${value}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
        
        <motion.div
          className="absolute top-1/2 -translate-y-1/2 w-5 h-5 bg-gradient-to-r from-accent-from to-accent-to rounded-full border-2 border-white shadow-lg cursor-pointer"
          style={{ left: `calc(${value}% - 10px)` }}
          whileHover={{ scale: 1.2 }}
          whileDrag={{ scale: 1.3 }}
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          onDrag={(event, info) => {
            const rect = event.currentTarget.parentElement?.getBoundingClientRect();
            if (rect) {
              const percentage = Math.max(0, Math.min(100, (info.point.x / rect.width) * 100));
              setValue(Math.round(percentage));
            }
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-accent-from to-accent-to rounded-full animate-pulse" />
        </motion.div>
        
>>>>>>> 90b8cac (feat: add React frontend application with modern UI #23)
        <input
          type="range"
          min="0"
          max="100"
          value={value}
<<<<<<< HEAD
          onChange={(e) => handleChange(Number(e.target.value))}
          className="atmosphere-slider-input absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          style={{ zIndex: 10, margin: 0, padding: 0 }}
        />

        {/* Visual Thumb */}
        <motion.div
          className="absolute top-1/2 -translate-y-1/2 w-5 h-5 bg-gradient-to-r from-accent-from to-accent-to rounded-full border-2 border-white shadow-lg pointer-events-none"
          style={{
            left: `calc(${value}% - 10px)`,
            zIndex: 5
          }}
          animate={{
            left: `calc(${value}% - 10px)`,
            scale: isHovering ? 1.2 : 1,
            boxShadow: isHovering
              ? '0 0 20px rgba(111, 0, 255, 0.5)'
              : '0 4px 8px rgba(0, 0, 0, 0.3)'
          }}
          transition={{ duration: 0.2 }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-accent-from to-accent-to rounded-full" />
        </motion.div>
=======
          onChange={(e) => setValue(Number(e.target.value))}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />
>>>>>>> 90b8cac (feat: add React frontend application with modern UI #23)
      </div>
    </div>
  );
};

export default AtmosphereSlider;